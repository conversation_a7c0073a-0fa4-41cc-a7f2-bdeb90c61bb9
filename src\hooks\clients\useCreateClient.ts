"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";

interface CreateClientData {
  name: string;
  matriculeFiscale: string;
  email?: string;
  phoneNumber?: string;
}

const useCreateClient = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createClient = async (client: CreateClientData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/clients", client);
      router.push("/clients"); // Redirect after successful creation
    } catch (err: any) {
      const code = err?.response?.data?.code;
      const message = err?.response?.data?.message;

      if (code === 11000 || message?.includes("E11000")) {
        setError("Matricule fiscale existe déjà pour un autre client.");
      } else {
        setError(message || "Erreur lors de la création du client.");
      }
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createClient, loading, error };
};

export default useCreateClient;
