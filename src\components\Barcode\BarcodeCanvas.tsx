"use client";

import React, { useEffect, useRef } from "react";
import Barcode from "react-barcode";

interface BarcodeCanvasProps {
    value: string;
    onBarcodeRendered: (dataUrl: string) => void;
}

const BarcodeCanvas: React.FC<BarcodeCanvasProps> = ({ value, onBarcodeRendered }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const barcodeRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const renderBarcode = async () => {
            if (barcodeRef.current && canvasRef.current) {
                const svg = barcodeRef.current.querySelector("svg");
                if (svg) {
                    const canvas = canvasRef.current;
                    const ctx = canvas.getContext("2d");

                    if (ctx) {
                        // Set a much higher scale for printing machines
                        const scale = 8; // Much higher scale for large barcode printing
                        const canvasWidth = 400 * scale; // Larger base width
                        const canvasHeight = 150 * scale; // Larger base height

                        // Set canvas size to larger dimensions
                        canvas.width = canvasWidth;
                        canvas.height = canvasHeight;

                        // Scale the drawing context for higher resolution
                        ctx.scale(scale, scale);

                        // Convert SVG to image for rendering on canvas
                        const svgData = new XMLSerializer().serializeToString(svg);

                        try {
                            // Use Promise-based approach for better performance
                            const img = new Image();
                            const imageLoadPromise = new Promise<void>((resolve, reject) => {
                                img.onload = () => {
                                    try {
                                        // Draw image on canvas (considering the scale factor)
                                        ctx.drawImage(img, 0, 0, canvasWidth / scale, canvasHeight / scale);
                                        const dataUrl = canvas.toDataURL("image/png", 0.95); // Slightly compressed for speed

                                        // Use requestAnimationFrame for better performance
                                        requestAnimationFrame(() => {
                                            onBarcodeRendered(dataUrl);
                                        });
                                        resolve();
                                    } catch (error) {
                                        reject(error);
                                    }
                                };
                                img.onerror = reject;
                            });

                            img.src = `data:image/svg+xml;base64,${window.btoa(svgData)}`;
                            await imageLoadPromise;

                        } catch (error) {
                            console.error("Failed to load barcode image", error);
                        }
                    }
                }
            }
        };

        renderBarcode();
    }, [value, onBarcodeRendered]);

    return (
        <div style={{ display: "none" }} ref={barcodeRef}>
            <Barcode value={value} width={3} height={80} fontSize={24} font={"bold"} />
            {/* Much larger canvas size for printing machine quality */}
            <canvas ref={canvasRef} width={400} height={150} />
        </div>
    );
};

export default BarcodeCanvas;
