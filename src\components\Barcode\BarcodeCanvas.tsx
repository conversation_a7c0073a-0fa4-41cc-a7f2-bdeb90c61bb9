"use client";

import React, { useEffect, useRef } from "react";
import Barcode from "react-barcode";

interface BarcodeCanvasProps {
    value: string;
    onBarcodeRendered: (dataUrl: string) => void;
}

const BarcodeCanvas: React.FC<BarcodeCanvasProps> = ({ value, onBarcodeRendered }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const barcodeRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const renderBarcode = () => {
            if (barcodeRef.current && canvasRef.current) {
                const svg = barcodeRef.current.querySelector("svg");
                if (svg) {
                    const canvas = canvasRef.current;
                    const ctx = canvas.getContext("2d");

                    if (ctx) {
                        // Set a higher scale for the canvas
                        const scale = 3; // Adjust the scale for higher quality (DPI)
                        const canvasWidth = 150 * scale;
                        const canvasHeight = 50 * scale;

                        // Set canvas size to larger dimensions
                        canvas.width = canvasWidth;
                        canvas.height = canvasHeight;

                        // Scale the drawing context for higher resolution
                        ctx.scale(scale, scale);

                        // Convert SVG to image for rendering on canvas
                        const svgData = new XMLSerializer().serializeToString(svg);
                        const img = new Image();
                        img.src = `data:image/svg+xml;base64,${window.btoa(svgData)}`;
                        img.onload = () => {
                            // Draw image on canvas (considering the scale factor)
                            ctx.drawImage(img, 0, 0, canvasWidth / scale, canvasHeight / scale);
                            const dataUrl = canvas.toDataURL("image/png");

                            // Return the data URL of the high-quality barcode
                            onBarcodeRendered(dataUrl);
                        };
                        img.onerror = (error) => {
                            console.error("Failed to load barcode image", error);
                        };
                    }
                }
            }
        };

        renderBarcode();
    }, [value, onBarcodeRendered]);

    return (
        <div style={{ display: "none" }} ref={barcodeRef}>
            <Barcode value={value} width={1} height={30} fontSize={14} font={"bold"} />
            {/* Increased the canvas size for higher quality */}
            <canvas ref={canvasRef} width={150} height={50} />
        </div>
    );
};

export default BarcodeCanvas;
