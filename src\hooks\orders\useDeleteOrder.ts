"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteOrder = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteOrder = async (orderId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/orders/${orderId}`);
      router.replace("/orders"); // Faster redirect instead of refresh
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de la commande.");
      setLoading(false);
    }
    // Don't set loading to false on success - let the redirect handle it
  };

  return { deleteOrder, loading, error };
};

export default useDeleteOrder;
