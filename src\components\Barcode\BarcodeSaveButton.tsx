"use client";

import React, { useEffect, useMemo, useState } from "react";
import jsPDF from "jspdf";
import { Lo<PERSON>, Printer, PrinterIcon } from "lucide-react";
import BarcodeCanvas from "./BarcodeCanvas";
import { Button } from "../ui-elements/button";

interface QRCodeData {
  qrValue: string;
  qrQuantity: number;
}

interface QRCodeSaveButtonProps {
  QrCodes: QRCodeData[];
  Type: string;         //OF ; PACKET ; COLIS ; PIECES
  BtnText:string;
}

const BarcodeSaveButton = ({ QrCodes,Type,BtnText }: QRCodeSaveButtonProps) => {
  const [qrCodeDataUrls, setQRCodeDataUrls] = useState<string[]>([]);
  const [printClicked, setPrintClicked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [repeatCount, setRepeatCount] = useState<number>(1);
  const [DateQR, setDateQR] = useState<Date>(new Date());
  const [showModal, setShowModal] = useState(false);

  const setQRCodeDataUrl = (index: number, url: string) => {
    setQRCodeDataUrls((prev) => {
      const newUrls = [...prev];
      newUrls[index] = url;
      return newUrls;
    });
  };



  const generatePDF = () => {
    const doc = new jsPDF("portrait", "mm", "a4");
    const qrCodeWidth = 45;
    const qrCodeHeight = 18;
    const paddingX = 25;
    const paddingY = 19;
    const qrPerRow = 3;
    const maxRowsPerPage = 8;

    let x = 8;
    let y = 10;
    let rowCount = 0;
    let qrInCurrentRow = 0;

    // Set font once at the beginning
    doc.setFont("helvetica", "normal");
    doc.setFontSize(8);

    expandedQrCodes.forEach(({ qrValue, qrQuantity }, qrIndex) => {
      const totalPieces = qrQuantity || 0;
      const pieceLabel = `${totalPieces}P`;

      const today = new Date(DateQR);
      const formattedDate = `${today.getDate().toString().padStart(2, "0")}/${(today.getMonth() + 1)
        .toString()
        .padStart(2, "0")}/${today.getFullYear()}`;

      const labelText = `${pieceLabel}    ${formattedDate}`;
      const centerX = x + qrCodeWidth / 2;
      const labelWidth = doc.getTextWidth(labelText);

      // Print label 1mm above barcode
      doc.text(labelText, centerX - labelWidth / 2, y -0.5);

      // Draw barcode
      doc.addImage(qrCodeDataUrls[qrIndex], "PNG", x, y, qrCodeWidth, qrCodeHeight);

      // Layout
      x += qrCodeWidth + paddingX;
      qrInCurrentRow++;
      if (qrInCurrentRow >= qrPerRow) {
        x = 8;
        y += qrCodeHeight + paddingY;
        rowCount++;
        qrInCurrentRow = 0;

        if (rowCount >= maxRowsPerPage) {
          doc.addPage();
          x = 8;
          y = 10;
          rowCount = 0;
        }
      }
    });
  if(Type==="OF"){
    doc.save(`${expandedQrCodes[0]?.qrValue || "Barcodes"}_Barcodes.pdf`);

  }
  if(Type==="PACKET"){
    doc.save(`Packets barcodes du OF N°${expandedQrCodes[0]?.qrValue.split("/")[0] || ""}.pdf`);

  }
  if(Type==="COLIS"){
    doc.save(`Colis barcodes du OF N°${expandedQrCodes[0]?.qrValue.split("/")[0] || ""}.pdf`);

  }

    setPrintClicked(false);
    setLoading(false);
    setProcessing(false);
  };


  const expandedQrCodes = useMemo(() => {
    return QrCodes.flatMap((qr) =>
      Array.from({ length: repeatCount }, () => ({
        ...qr,
        qrQuantity: qr.qrQuantity,
      }))
    );
  }, [QrCodes, repeatCount]);

  useEffect(() => {
    if (
      printClicked &&
      !processing &&
      qrCodeDataUrls.length === expandedQrCodes.length &&
      qrCodeDataUrls.every((url) => url)
    ) {
      setProcessing(true);
      generatePDF();
    }
  }, [qrCodeDataUrls, expandedQrCodes.length, printClicked, processing]);

  const startPrint = () => {
    setQRCodeDataUrls(new Array(expandedQrCodes.length).fill("")); // reset URLs

    setLoading(true);
    setPrintClicked(true);
  };

  return (
    <div>
      {Type!="OF" &&
      <Button label={BtnText}icon={<Printer/>} size={"small"} variant={"gray"} shape={"rounded"} onClick={() => setShowModal(true)}/>
}
      {Type==="OF" &&
      <Printer className="cursor-pointer" onClick={() => setShowModal(true)} />
      }

      {showModal && (
  <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
    <div className="bg-white p-6 rounded shadow-lg text-center w-auto">
      <h2 className="text-lg font-bold mb-2">Combien de fois imprimer chaque code ?</h2>
      <input
        type="number"
        value={repeatCount}
        onChange={(e) => setRepeatCount(Number(e.target.value))}
        className="border px-2 py-1 w-full mb-4"
        min={1}
      />
      <h2 className="text-md font-bold mb-2">Sélectionner la date à afficher sur le code-barres</h2>
            <input
              type="date"
              value={DateQR.toISOString().split("T")[0]} // Format the date for the input field
              onChange={(e) => setDateQR(new Date(e.target.value))}
              className="border px-2 py-1 w-full mb-4"
            />
      <div className="flex justify-center gap-4 flex-wrap">
        <Button
                variant={"dark"}
                shape="rounded"
                size="small"
                icon={<PrinterIcon />}
                onClick={() => {
                  setShowModal(false);
                  startPrint();
                } } label={"Confirmer"}       />

        <Button
                shape={"rounded"}
                size={"small"}

                onClick={() => setShowModal(false)}
                label={"Annuler"}        />

      </div>
    </div>
  </div>
)}


      {printClicked &&
        expandedQrCodes.map((qrCodeData, index) => (
          <BarcodeCanvas key={index} value={qrCodeData.qrValue} onBarcodeRendered={(url) => setQRCodeDataUrl(index, url)} />
        ))}

      {loading && <Loader />}
    </div>
  );
};

export default BarcodeSaveButton;
