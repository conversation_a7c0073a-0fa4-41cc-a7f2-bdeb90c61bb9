"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteColis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteColis = async (colisId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/colis/${colisId}`);
      router.refresh();
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression du colis.");
    } finally {
      setLoading(false);
    }
  };

  return { deleteColis, loading, error };
};

export default useDeleteColis;
