"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteUser = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteUser = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/users/${userId}`);
      router.refresh(); // Refresh user list or use router.push("/users") if needed
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de l'utilisateur.");
    } finally {
      setLoading(false);
    }
  };

  return { deleteUser, loading, error };
};

export default useDeleteUser;
