"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteOperation = async (operationId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/operations/${operationId}`);
      router.replace("/operations"); // Faster redirect instead of refresh
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de l'opération.");
      setLoading(false);
    }
    // Don't set loading to false on success - let the redirect handle it
  };

  return { deleteOperation, loading, error };
};

export default useDeleteOperation;
