"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteClient = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteClient = async (clientId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/clients/${clientId}`);
      router.refresh(); // Refresh the page to reflect deletion
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression du client.");
    } finally {
      setLoading(false);
    }
  };

  return { deleteClient, loading, error };
};

export default useDeleteClient;
