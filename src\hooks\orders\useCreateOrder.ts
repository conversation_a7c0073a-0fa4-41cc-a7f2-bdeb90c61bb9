"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import { useLoading } from "@/context/LoadingContext";
import { CreateOrderData } from "@/types/models";

const useCreateOrder = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createOrder = async (order: CreateOrderData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/orders", order);
      router.push("/orders"); // Redirect to order list or success page
    } catch (err: any) {
        const code = err?.response?.data?.code;
        const message = err?.response?.data?.message;

        if (code === 11000 || message?.includes("E11000")) {
          setError("Il existe déjà un ordre de fabrication avec le même numéro.");
        } else {
          setError(message || "Erreur lors de la création de la commande.");
        }
      }finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createOrder, loading, error };
};

export default useCreateOrder;
