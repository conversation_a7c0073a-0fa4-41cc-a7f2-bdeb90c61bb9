"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

interface CreateArticleData {
  ref: string;
  model: string;
  gamme?: string; // Optional
  client: string;
}

const useCreateArticle = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createArticle = async (article: CreateArticleData) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/articles", article);
      router.push("/articles"); // Adjust this path if needed
    } catch (err: any) {
      const message = err?.response?.data?.message;

      if (message?.includes("Référence d'article déjà utilisée") || message?.includes("E11000")) {
        setError("Référence d'article déjà utilisée.");
      } else {
        setError(message || "Erreur lors de la création de l'article."); 
      }
    } finally { 
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createArticle, loading, error };
};

export default useCreateArticle;
