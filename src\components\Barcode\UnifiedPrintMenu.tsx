"use client";

import React, { useState, useEffect, useMemo } from "react";
import jsPD<PERSON> from "jspdf";
import { Lo<PERSON>, Printer, PrinterIcon, X } from "lucide-react";
import BarcodeCanvas from "./BarcodeCanvas";
import { Button } from "../ui-elements/button";
import { Order } from "@/types/models";

interface UnifiedPrintMenuProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
}

interface PrintOptions {
  includeOF: boolean;
  includeColis: boolean;
  includePackets: boolean;
  ofQuantity: number;
  colisQuantity: number;
  packetsQuantity: number;
  date: Date;
}

const UnifiedPrintMenu = ({ order, isOpen, onClose }: UnifiedPrintMenuProps) => {
  const [printOptions, setPrintOptions] = useState<PrintOptions>({
    includeOF: true,
    includeColis: true,
    includePackets: true,
    ofQuantity: 1,
    colisQuantity: 1,
    packetsQuantity: 1,
    date: new Date(),
  });

  const [qrCodeDataUrls, setQRCodeDataUrls] = useState<string[]>([]);
  const [printClicked, setPrintClicked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);

  const setQRCodeDataUrl = (index: number, url: string) => {
    setQRCodeDataUrls((prev) => {
      const newUrls = [...prev];
      newUrls[index] = url;
      return newUrls;
    });
  };

  // Generate the complete QR codes list based on selected options
  const allQrCodes = useMemo(() => {
    const codes: Array<{ qrValue: string; qrQuantity: number; type: string }> = [];

    // Add OF barcodes
    if (printOptions.includeOF) {
      for (let i = 0; i < printOptions.ofQuantity; i++) {
        codes.push({
          qrValue: order.qrCode,
          qrQuantity: order.totalPieces,
          type: "OF"
        });
      }
    }

    // Add Colis barcodes
    if (printOptions.includeColis) {
      order.colis.forEach((colis: any) => {
        for (let i = 0; i < printOptions.colisQuantity; i++) {
          codes.push({
            qrValue: colis.qrCode,
            qrQuantity: colis.packets?.length || 0,
            type: "COLIS"
          });
        }
      });
    }

    // Add Packets barcodes
    if (printOptions.includePackets) {
      order.colis.forEach((colis: any) => {
        if (colis.packets && Array.isArray(colis.packets)) {
          colis.packets.forEach((packet: any) => {
            for (let i = 0; i < printOptions.packetsQuantity; i++) {
              codes.push({
                qrValue: packet.qrCode || `${order.orderNumber}/C${colis.numeroColis}/P${packet.numero}`,
                qrQuantity: packet.pieces?.length || packet.quantite || 0,
                type: "PACKET"
              });
            }
          });
        }
      });
    }

    return codes;
  }, [order, printOptions]);

  const generatePDF = () => {
    const doc = new jsPDF("portrait", "mm", "a4");
    const qrCodeWidth = 60;
    const qrCodeHeight = 25;

    // Set font once at the beginning
    doc.setFont("helvetica", "normal");
    doc.setFontSize(12);

    const formattedDate = `${printOptions.date.getDate().toString().padStart(2, "0")}/${(printOptions.date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${printOptions.date.getFullYear()}`;

    allQrCodes.forEach(({ qrValue, qrQuantity, type }, qrIndex) => {
      // Add new page for each barcode (except the first one)
      if (qrIndex > 0) {
        doc.addPage();
      }

      // Center the barcode on the page
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      const x = (pageWidth - qrCodeWidth) / 2;
      const y = (pageHeight - qrCodeHeight) / 2;

      const pieceLabel = `${qrQuantity}P`;
      const labelText = `${pieceLabel}    ${formattedDate}`;
      const centerX = pageWidth / 2;
      const labelWidth = doc.getTextWidth(labelText);

      // Print label above barcode (centered)
      doc.text(labelText, centerX - labelWidth / 2, y - 5);

      // Draw barcode (centered)
      if (qrCodeDataUrls[qrIndex]) {
        doc.addImage(qrCodeDataUrls[qrIndex], "PNG", x, y, qrCodeWidth, qrCodeHeight);
      }
    });

    doc.save(`OF_${order.orderNumber}_Complete_Barcodes.pdf`);
    setPrintClicked(false);
    setLoading(false);
    setProcessing(false);
    onClose();
  };

  useEffect(() => {
    if (
      printClicked &&
      !processing &&
      qrCodeDataUrls.length === allQrCodes.length &&
      qrCodeDataUrls.every((url) => url)
    ) {
      setProcessing(true);
      generatePDF();
    }
  }, [qrCodeDataUrls, allQrCodes.length, printClicked, processing]);

  const startPrint = () => {
    setQRCodeDataUrls(new Array(allQrCodes.length).fill(""));
    setLoading(true);
    setPrintClicked(true);
  };

  const handleOptionChange = (key: keyof PrintOptions, value: any) => {
    setPrintOptions(prev => ({ ...prev, [key]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-96 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold">Imprimer les codes-barres</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          {/* Date Selection */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Date à afficher sur les codes-barres
            </label>
            <input
              type="date"
              value={printOptions.date.toISOString().split("T")[0]}
              onChange={(e) => handleOptionChange('date', new Date(e.target.value))}
              className="w-full border px-3 py-2 rounded"
            />
          </div>

          {/* OF Options */}
          <div className="border rounded p-3">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includeOF"
                checked={printOptions.includeOF}
                onChange={(e) => handleOptionChange('includeOF', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="includeOF" className="font-medium">
                Ordre de Fabrication (OF)
              </label>
            </div>
            {printOptions.includeOF && (
              <div className="ml-6">
                <label className="block text-sm mb-1">Quantité:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.ofQuantity}
                  onChange={(e) => handleOptionChange('ofQuantity', Number(e.target.value))}
                  className="w-20 border px-2 py-1 rounded"
                />
              </div>
            )}
          </div>

          {/* Colis Options */}
          <div className="border rounded p-3">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includeColis"
                checked={printOptions.includeColis}
                onChange={(e) => handleOptionChange('includeColis', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="includeColis" className="font-medium">
                Colis ({order.colis?.length || 0} colis)
              </label>
            </div>
            {printOptions.includeColis && (
              <div className="ml-6">
                <label className="block text-sm mb-1">Quantité par colis:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.colisQuantity}
                  onChange={(e) => handleOptionChange('colisQuantity', Number(e.target.value))}
                  className="w-20 border px-2 py-1 rounded"
                />
              </div>
            )}
          </div>

          {/* Packets Options */}
          <div className="border rounded p-3">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includePackets"
                checked={printOptions.includePackets}
                onChange={(e) => handleOptionChange('includePackets', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="includePackets" className="font-medium">
                Paquets ({order.colis?.reduce((acc: number, colis: any) => acc + (colis.packets?.length || 0), 0) || 0} paquets)
              </label>
            </div>
            {printOptions.includePackets && (
              <div className="ml-6">
                <label className="block text-sm mb-1">Quantité par paquet:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.packetsQuantity}
                  onChange={(e) => handleOptionChange('packetsQuantity', Number(e.target.value))}
                  className="w-20 border px-2 py-1 rounded"
                />
              </div>
            )}
          </div>

          {/* Summary */}
          <div className="bg-gray-50 p-3 rounded">
            <p className="text-sm font-medium mb-1">Résumé:</p>
            <p className="text-sm">Total codes-barres: {allQrCodes.length}</p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              onClick={onClose}
              variant="gray"
              size="small"
              label="Annuler"
            />
            <Button
              onClick={startPrint}
              variant="dark"
              size="small"
              icon={<PrinterIcon />}
              label="Imprimer"
              disabled={loading || allQrCodes.length === 0}
            />
          </div>
        </div>

        {/* Hidden barcode canvases */}
        {printClicked &&
          allQrCodes.map((qrCodeData, index) => (
            <BarcodeCanvas
              key={index}
              value={qrCodeData.qrValue}
              onBarcodeRendered={(url) => setQRCodeDataUrl(index, url)}
            />
          ))}

        {loading && (
          <div className="flex items-center justify-center mt-4">
            <Loader className="animate-spin mr-2" />
            <span>Génération en cours...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedPrintMenu;
