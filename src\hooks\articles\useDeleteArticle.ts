"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";

const useDeleteArticle = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const deleteArticle = async (articleId: string) => {
    setLoading(true);
    setError(null);

    try {
      await axiosInstance.delete(`/articles/${articleId}`);
      router.replace("/articles"); // Faster redirect instead of refresh
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la suppression de l'article.");
      setLoading(false);
    }
    // Don't set loading to false on success - let the redirect handle it
  };

  return { deleteArticle, loading, error };
};

export default useDeleteArticle;
