/**
 * Barcode generation optimization utilities
 * Provides faster batch processing for large quantities of barcodes
 */

// Batch size for processing barcodes in chunks
export const BATCH_SIZE = 10;

// Delay between batches to prevent browser freezing
export const BATCH_DELAY = 50; // milliseconds

/**
 * Process barcodes in batches to prevent browser freezing
 * @param items Array of items to process
 * @param batchSize Number of items per batch
 * @param processor Function to process each batch
 * @param onProgress Callback for progress updates
 */
export const processBatches = async <T>(
  items: T[],
  batchSize: number,
  processor: (batch: T[], startIndex: number) => Promise<void>,
  onProgress?: (completed: number, total: number) => void
): Promise<void> => {
  const total = items.length;
  let completed = 0;

  for (let i = 0; i < total; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    // Process the batch
    await processor(batch, i);
    
    completed += batch.length;
    
    // Update progress
    if (onProgress) {
      onProgress(completed, total);
    }
    
    // Add small delay between batches to prevent freezing
    if (i + batchSize < total) {
      await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
    }
  }
};

/**
 * Optimized canvas creation for barcode rendering
 * Reuses canvas elements to reduce memory allocation
 */
export class CanvasPool {
  private static instance: CanvasPool;
  private canvases: HTMLCanvasElement[] = [];
  private maxPoolSize = 5;

  static getInstance(): CanvasPool {
    if (!CanvasPool.instance) {
      CanvasPool.instance = new CanvasPool();
    }
    return CanvasPool.instance;
  }

  getCanvas(): HTMLCanvasElement {
    if (this.canvases.length > 0) {
      return this.canvases.pop()!;
    }
    
    // Create new canvas if pool is empty
    const canvas = document.createElement('canvas');
    canvas.width = 400 * 8; // High resolution
    canvas.height = 150 * 8;
    return canvas;
  }

  returnCanvas(canvas: HTMLCanvasElement): void {
    if (this.canvases.length < this.maxPoolSize) {
      // Clear the canvas
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
      this.canvases.push(canvas);
    }
  }
}

/**
 * Optimized barcode data URL generation
 * Uses canvas pooling and optimized rendering
 */
export const generateBarcodeDataUrl = async (
  svgElement: SVGElement,
  quality: number = 0.95
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      const canvasPool = CanvasPool.getInstance();
      const canvas = canvasPool.getCanvas();
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      const scale = 8;
      const canvasWidth = 400 * scale;
      const canvasHeight = 150 * scale;

      // Set canvas size
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      // Scale context
      ctx.scale(scale, scale);

      // Convert SVG to image
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const img = new Image();

      img.onload = () => {
        try {
          // Draw image on canvas
          ctx.drawImage(img, 0, 0, canvasWidth / scale, canvasHeight / scale);
          
          // Generate data URL
          const dataUrl = canvas.toDataURL('image/png', quality);
          
          // Return canvas to pool
          canvasPool.returnCanvas(canvas);
          
          resolve(dataUrl);
        } catch (error) {
          canvasPool.returnCanvas(canvas);
          reject(error);
        }
      };

      img.onerror = () => {
        canvasPool.returnCanvas(canvas);
        reject(new Error('Failed to load SVG image'));
      };

      img.src = `data:image/svg+xml;base64,${window.btoa(svgData)}`;
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Performance monitoring for barcode generation
 */
export class PerformanceMonitor {
  private startTime: number = 0;
  private itemsProcessed: number = 0;

  start(): void {
    this.startTime = performance.now();
    this.itemsProcessed = 0;
  }

  incrementProcessed(): void {
    this.itemsProcessed++;
  }

  getStats(): { itemsPerSecond: number; totalTime: number; averageTime: number } {
    const totalTime = performance.now() - this.startTime;
    const itemsPerSecond = (this.itemsProcessed / totalTime) * 1000;
    const averageTime = totalTime / this.itemsProcessed;

    return {
      itemsPerSecond: Math.round(itemsPerSecond * 100) / 100,
      totalTime: Math.round(totalTime),
      averageTime: Math.round(averageTime * 100) / 100
    };
  }
}
