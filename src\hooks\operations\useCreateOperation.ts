"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import axiosInstance from "@/utils/axiosInstance";
import { useLoading } from "@/context/LoadingContext";

export interface CreateOperationData {
  name: string;
}

const useCreateOperation = () => {
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { setLoading } = useLoading();

  const createOperations = async (operations: CreateOperationData[]) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      await axiosInstance.post("/operations", operations);
      router.push("/operations"); // Adjust if your route is different
    } catch (err: any) {
      const message = err?.response?.data?.message;
      setError(message || "Erreur lors de la création des opérations.");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { createOperations, loading, error };
};

export default useCreateOperation;
