'use client';

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui-elements/button";
import { PlusIcon } from "lucide-react";
import { useLoading } from "@/context/LoadingContext";

const NewUserButton = () => {
  const router = useRouter();
  const { setLoading } = useLoading();
  const handleClick = () => {
    setLoading(true)
    router.push("/users/nouveauUser");
    setLoading(false)
  };

  return (
    <Button
      label="Nouveau Utilisateur"
      variant="dark"
      shape="rounded"
      size="small"
      icon={<PlusIcon />}
      onClick={handleClick}
    />
  );
};

export default NewUserButton;
