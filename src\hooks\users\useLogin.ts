"use client";

import { useState } from "react";
import axiosInstance from "@/utils/axiosInstance";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";
import { useLoading } from "@/context/LoadingContext";

interface DecodedToken {
    name: string;
    // Add other fields as needed (e.g., email, exp)
  }

interface LoginResponse {
  token: string;
}

const useLogin = () => {
  const router = useRouter();
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  const login = async (email: string, password: string) => {
    setLoading(true);
    setLoading2(true);
    setError(null);

    try {
      const response = await axiosInstance.post<LoginResponse>("/users/login", {
        email,
        password,
      });
      const { token } = response.data;
      // Decode the token
      const decoded: DecodedToken = jwtDecode(token);

      // Save token in cookies
      Cookies.set("token", token, { expires: 1 });
      Cookies.set("name", decoded.name, { expires: 1 });

      // Redirect immediately after successful login
      router.replace("/");
    } catch (err) {
      setError("Login failed. Please check your credentials.");
      setLoading(false);
      setLoading2(false);
    }
    // Don't set loading to false on success - let the redirect handle it
  };

  return { login, loading, error };
};

export default useLogin;
