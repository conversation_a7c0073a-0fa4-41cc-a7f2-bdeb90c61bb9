"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { useLoading } from "@/context/LoadingContext";

const useLogout = () => {
  const router = useRouter();
  const [loading, setLoading2] = useState(false);
  const { setLoading } = useLoading();

  const logout = async () => {
    setLoading(true);
    setLoading2(true);

    try {
      // Remove all authentication-related cookies
      Cookies.remove("token");
      Cookies.remove("name");
      Cookies.remove("email");
      
      // Optional: You can also call a logout API endpoint here if needed
      // await axiosInstance.post("/users/logout");
      
      // Redirect to sign-in page
      router.push("/auth/sign-in");
    } catch (error) {
      console.error("Logout error:", error);
      // Even if there's an error, still redirect to sign-in
      router.push("/auth/sign-in");
    } finally {
      setLoading(false);
      setLoading2(false);
    }
  };

  return { logout, loading };
};

export default useLogout;
